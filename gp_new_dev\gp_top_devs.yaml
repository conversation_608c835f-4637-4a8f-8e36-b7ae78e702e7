# 游戏数据抓取配置文件

# 输出文件配置
output:
  filename: "output/gp_developers.json"  # 输出的JSON文件名

# 抓取参数配置
scraping:
  num_per_request: 200              # 每次请求抓取的游戏数量
  delay_ms: 100                     # 请求间隔延迟（毫秒）
  language: "en"                    # 语言设置

# 游戏品类配置（所有带GAME的品类）
categories:
  - "GAME"
  - "GAME_ACTION"
  - "GAME_CASUAL"
  - "GAME_ADVENTURE"
  - "GAME_ARCADE"
  - "GAME_BOARD"
  - "GAME_CARD"
  - "GAME_CASINO"
  - "GAME_EDUCATIONAL"
  - "GAME_MUSIC"
  - "GAME_PUZZLE"
  - "GAME_RACING"
  - "GAME_ROLE_PLAYING"
  - "GAME_SIMULATION"
  - "GAME_SPORTS"
  - "GAME_STRATEGY"
  - "GAME_TRIVIA"
  - "GAME_WORD"

# Collections配置（排行榜类型）
collections:
  - "TOP_FREE"      # 热门免费
  - "TOP_PAID"      # 热门付费
  - "GROSSING"      # 畅销榜

# 国家配置（支持多个国家）
countries:
  - code: "us"      # 美国
    name: "United States"
  - code: "jp"      # 日本
    name: "Japan"
  - code: "kr"      # 韩国
    name: "South Korea"
  - code: "gb"      # 英国
    name: "United Kingdom"
  - code: "de"      # 德国
    name: "Germany"
  - code: "fr"      # 法国
    name: "France"
  - code: "in"      # 印度
    name: "India"
  - code: "br"      # 巴西
    name: "Brazil"
  - code: "ca"      # 加拿大
    name: "Canada"
  - code: "au"      # 澳大利亚
    name: "Australia"
  - code: "mx"      # 墨西哥
    name: "Mexico"
  - code: "it"      # 意大利
    name: "Italy"
  - code: "es"      # 西班牙
    name: "Spain"
  # 更多国家可以根据需要添加
  # - code: "ru"      # 俄罗斯
  #   name: "Russia"
  # - code: "cn"      # 中国（某些API可能不支持）
  #   name: "China"
# 调试配置
debug:
  show_progress: true               # 显示抓取进度
  show_preview: true                # 显示数据预览
  preview_count: 10                 # 预览显示的厂商数量
