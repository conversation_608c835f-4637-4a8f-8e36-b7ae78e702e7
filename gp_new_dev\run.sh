#!/bin/bash

# VOODOO App Scraper 运行脚本

echo "🚀 VOODOO App Scraper"
echo "===================="

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 检查是否存在 package.json
if [ ! -f "package.json" ]; then
    echo "❌ package.json 文件不存在"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

echo "🔍 开始获取 VOODOO 应用数据..."
echo "提示：这个过程可能需要几分钟时间..."

# 运行程序
node scraper.js

# 检查运行结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 程序执行完成！"
    echo "📁 查看 output 目录获取结果文件"
else
    echo ""
    echo "❌ 程序执行过程中出现错误"
    exit 1
fi
