@echo off
chcp 65001 >nul
title VOODOO App Scraper

echo 🚀 VOODOO App Scraper
echo ====================

REM 检查 Node.js 是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 版本:
node --version

REM 检查是否存在 package.json
if not exist "package.json" (
    echo ❌ package.json 文件不存在
    pause
    exit /b 1
)

REM 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 安装依赖包...
    call npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo 🔍 开始获取 VOODOO 应用数据...
echo 提示：这个过程可能需要几分钟时间...

REM 运行程序
node scraper.js

REM 检查运行结果
if errorlevel 1 (
    echo.
    echo ❌ 程序执行过程中出现错误
    pause
    exit /b 1
) else (
    echo.
    echo 🎉 程序执行完成！
    echo 📁 查看 output 目录获取结果文件
    pause
)
