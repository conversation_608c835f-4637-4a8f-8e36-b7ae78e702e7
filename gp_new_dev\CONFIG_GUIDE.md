# 游戏厂商数据抓取工具 - 配置指南

这个工具可以从Google Play Store抓取游戏厂商和对应的游戏数据，支持多国家、多品类、多排行榜的配置化抓取。

## ✅ 已完成的功能

- **配置化抓取**: 通过YAML文件灵活配置抓取参数
- **多国家支持**: 支持抓取不同国家的游戏数据
- **多品类支持**: 支持所有游戏品类的数据抓取
- **多排行榜支持**: 支持热门免费、热门付费、畅销榜等
- **数据追加**: 支持在现有数据基础上追加新数据
- **自动去重**: 厂商和游戏名称自动去重
- **错误处理**: 完善的错误处理和进度显示

## 📁 文件说明

- `test1.js` - 主程序文件
- `config.yaml` - 配置文件
- `game_developers.json` - 输出的数据文件

## 🔧 配置说明

### 输出文件配置
```yaml
output:
  filename: "game_developers.json"  # 可以修改为任何文件名
```

### 抓取参数配置
```yaml
scraping:
  num_per_request: 250              # 每次请求抓取的游戏数量 (建议100-250)
  delay_ms: 500                     # 请求间隔延迟（毫秒，建议500-1000）
  language: "en"                    # 语言设置 (en, zh, ja, ko等)
```

### 🎮 游戏品类配置

在 `categories` 部分添加或删除您需要的游戏品类：

```yaml
categories:
  - "GAME"                # 游戏总类
  - "GAME_ACTION"         # 动作游戏
  - "GAME_CASUAL"         # 休闲游戏
  # 取消注释下面的行来添加更多品类
  # - "GAME_ADVENTURE"    # 冒险游戏
  # - "GAME_ARCADE"       # 街机游戏
  # - "GAME_BOARD"        # 棋牌游戏
  # - "GAME_CARD"         # 卡牌游戏
  # - "GAME_CASINO"       # 赌场游戏
  # - "GAME_EDUCATIONAL"  # 教育游戏
  # - "GAME_MUSIC"        # 音乐游戏
  # - "GAME_PUZZLE"       # 益智游戏
  # - "GAME_RACING"       # 赛车游戏
  # - "GAME_ROLE_PLAYING" # 角色扮演游戏
  # - "GAME_SIMULATION"   # 模拟游戏
  # - "GAME_SPORTS"       # 体育游戏
  # - "GAME_STRATEGY"     # 策略游戏
  # - "GAME_TRIVIA"       # 问答游戏
  # - "GAME_WORD"         # 文字游戏
```

### 📊 Collections配置

在 `collections` 部分配置排行榜类型：

```yaml
collections:
  - "TOP_FREE"      # 热门免费
  - "TOP_PAID"      # 热门付费
  - "GROSSING"      # 畅销榜
  # 可选的其他collections:
  # - "NEW_FREE"    # 新上架免费
  # - "NEW_PAID"    # 新上架付费
```

### 🌍 国家配置

在 `countries` 部分添加或删除您需要的国家：

```yaml
countries:
  - code: "us"      # 美国
    name: "United States"
  - code: "jp"      # 日本
    name: "Japan"
  - code: "kr"      # 韩国
    name: "South Korea"
  - code: "gb"      # 英国
    name: "United Kingdom"
  - code: "de"      # 德国
    name: "Germany"
  # 取消注释下面的行来添加更多国家
  # - code: "fr"    # 法国
  #   name: "France"
  # - code: "in"    # 印度
  #   name: "India"
  # - code: "br"    # 巴西
  #   name: "Brazil"
  # - code: "ca"    # 加拿大
  #   name: "Canada"
  # - code: "au"    # 澳大利亚
  #   name: "Australia"
  # - code: "mx"    # 墨西哥
  #   name: "Mexico"
  # - code: "it"    # 意大利
  #   name: "Italy"
  # - code: "es"    # 西班牙
  #   name: "Spain"
  # - code: "ru"    # 俄罗斯
  #   name: "Russia"
```

### 🐛 调试配置

```yaml
debug:
  show_progress: true               # 显示抓取进度
  show_preview: true                # 显示数据预览
  preview_count: 10                 # 预览显示的厂商数量
```

## 🚀 使用方法

1. **修改配置**: 根据需要编辑 `config.yaml` 文件
2. **运行程序**: 
   ```bash
   node test1.js
   ```

## 📄 输出格式

生成的JSON文件格式：
```json
{
  "厂商名": ["游戏名1", "游戏名2", "游戏名3"],
  "Supercell": [
    "Clash Royale",
    "Clash of Clans", 
    "Brawl Stars"
  ]
}
```

## 💡 配置建议

### 快速测试配置
```yaml
categories:
  - "GAME_CASUAL"
collections:
  - "TOP_FREE"
countries:
  - code: "us"
    name: "United States"
scraping:
  num_per_request: 100
  delay_ms: 1000
```

### 全面抓取配置
```yaml
categories:
  - "GAME"
  - "GAME_ACTION"
  - "GAME_CASUAL"
  - "GAME_PUZZLE"
  - "GAME_STRATEGY"
collections:
  - "TOP_FREE"
  - "TOP_PAID"
  - "GROSSING"
countries:
  - code: "us"
    name: "United States"
  - code: "jp"
    name: "Japan"
  - code: "kr"
    name: "South Korea"
  - code: "gb"
    name: "United Kingdom"
  - code: "de"
    name: "Germany"
scraping:
  num_per_request: 250
  delay_ms: 500
```

## ⚠️ 注意事项

1. **请求频率**: 建议设置适当的延迟时间避免被限制
2. **国家支持**: 某些国家的API可能不稳定，建议先测试
3. **数据追加**: 多次运行会自动追加新数据并去重
4. **错误处理**: 单个请求失败不会影响整体抓取进程

## 📊 抓取规模估算

- **品类数** × **Collections数** × **国家数** × **每次抓取数量** = 总抓取量
- 例如: 3品类 × 3collections × 5国家 × 250游戏 = 11,250个游戏数据

根据您的需求调整配置以平衡抓取效率和数据完整性！
