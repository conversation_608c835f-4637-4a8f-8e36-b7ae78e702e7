# VOODOO App Scraper

这个程序用于获取 VOODOO 公司在 Google Play Store 上的所有应用信息。

## 功能特性

- 🔍 自动搜索 VOODOO 开发的所有应用
- 📊 获取详细的应用信息（评分、下载量、描述等）
- 💾 支持 JSON 和 CSV 格式导出
- 📋 生成详细的分析报告
- ⚡ 智能限速，避免被限制访问

## 安装步骤

1. 确保已安装 Node.js (版本 >= 14)

2. 安装依赖：
```bash
npm install
```

## 使用方法

运行程序：
```bash
npm start
```

或者：
```bash
node scraper.js
```

## 输出文件

程序会在 `output` 目录中生成以下文件：

- `voodoo_apps_basic.json` - 基础应用信息
- `voodoo_apps_detailed.json` - 详细应用信息（JSON格式）
- `voodoo_apps_detailed.csv` - 详细应用信息（CSV格式）
- `voodoo_report.md` - 分析报告

## 获取的信息包括

### 基本信息
- 应用名称、ID、图标
- 开发者信息
- Play Store 链接

### 详细信息
- 评分和评论统计
- 下载量和安装数据
- 应用分类和标签
- 版本信息和更新时间
- 应用大小和系统要求
- 价格信息（免费/付费）
- 广告和内购信息
- 应用截图和视频
- 最新用户评论

### 统计分析
- 分类分布统计
- 平均评分分析
- 热门应用排行
- 最新更新应用

## 注意事项

- 程序会自动限制请求速度，避免被 Google Play 限制
- 如果某个应用获取失败，程序会继续处理其他应用
- 建议在网络状况良好时运行程序
- 大量请求可能需要较长时间完成

## 故障排除

如果遇到网络错误或访问限制：
1. 检查网络连接
2. 等待一段时间后重试
3. 可以调整 `scraper.js` 中的延迟时间

## 许可证

MIT License
