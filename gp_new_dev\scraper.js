const gplay = require('google-play-scraper');
const fs = require('fs-extra');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const path = require('path');

class AppScraper {
    constructor(developerName = 'VOODOO') {
        this.developer = developerName;
        this.outputDir = './output';
        this.apps = [];
        this.detailedApps = [];
    }

    async init() {
        // 创建输出目录
        await fs.ensureDir(this.outputDir);
        console.log(`🚀 开始获取 ${this.developer} 的所有应用...`);
    }

    async getDeveloperApps() {
        try {
            console.log(`📱 搜索 ${this.developer} 开发的应用...`);

            // 通过开发者ID直接获取应用列表
            let allApps = [];

            try {
                console.log('🔍 尝试通过开发者ID获取应用...');

                // 尝试获取更多页面的应用
                let page = 0;
                let hasMore = true;

                while (hasMore && page < 10) { // 最多尝试10页
                    try {
                        const developerApps = await gplay.developer({
                            devId: this.developer,
                            num: 120, // 每页最多120个
                            start: page * 120 // 分页偏移
                        });

                        if (developerApps.length === 0) {
                            hasMore = false;
                            break;
                        }

                        console.log(`📱 第${page + 1}页找到 ${developerApps.length} 个应用`);

                        // 合并结果，避免重复
                        developerApps.forEach(app => {
                            if (!allApps.find(existing => existing.appId === app.appId)) {
                                allApps.push(app);
                            }
                        });

                        page++;

                        // 如果返回的应用数量少于请求数量，说明没有更多了
                        if (developerApps.length < 120) {
                            hasMore = false;
                        }

                        // 添加延迟避免被限制
                        if (hasMore) {
                            await this.sleep(2000);
                        }

                    } catch (pageError) {
                        console.log(`⚠️ 获取第${page + 1}页失败:`, pageError.message);
                        hasMore = false;
                    }
                }

                console.log(`📱 通过开发者ID总共找到 ${allApps.length} 个应用`);
            } catch (devError) {
                console.log('⚠️ 开发者ID方法失败:', devError.message);
            }

            this.apps = allApps;
            console.log(`✅ 总共找到 ${this.apps.length} 个 ${this.developer} 应用`);

            // 保存基础信息
            await this.saveBasicInfo();

            return this.apps;
        } catch (error) {
            console.error('❌ 搜索应用时出错:', error.message);
            throw error;
        }
    }

    async getDetailedInfo() {
        console.log('📊 获取详细信息...');
        
        for (let i = 0; i < this.apps.length; i++) {
            const app = this.apps[i];
            try {
                console.log(`📋 处理 ${i + 1}/${this.apps.length}: ${app.title}`);
                
                const details = await gplay.app({ appId: app.appId });
                
                // 获取评论
                let reviews = [];
                try {
                    reviews = await gplay.reviews({
                        appId: app.appId,
                        sort: gplay.sort.NEWEST,
                        num: 10
                    });
                } catch (reviewError) {
                    console.log(`⚠️  无法获取 ${app.title} 的评论: ${reviewError.message}`);
                }

                const detailedApp = {
                    // 截图和媒体
                    screenshotUrls: details.screenshots || [],
                    ipadScreenshotUrls: [], // Google Play 没有iPad截图概念
                    appletvScreenshotUrls: [], // Google Play 没有Apple TV截图概念

                    // 图标
                    artworkUrl512: details.icon,
                    artworkUrl60: details.icon,
                    artworkUrl100: details.icon,

                    // 开发者信息
                    artistViewUrl: details.url,
                    artistId: details.developerId,
                    artistName: details.developer,
                    sellerName: details.developer,
                    sellerUrl: details.developerWebsite || "",

                    // 设备支持（Google Play 没有具体设备列表）
                    supportedDevices: [],

                    // 功能特性
                    features: [],
                    advisories: details.contentRatingDescription ? [details.contentRatingDescription] : [],

                    // 游戏中心（Google Play 没有此概念）
                    isGameCenterEnabled: false,

                    // 基本信息
                    kind: "software",
                    averageUserRating: details.score,
                    averageUserRatingForCurrentVersion: details.score,
                    languageCodesISO2A: [],
                    fileSizeBytes: details.size ? details.size.toString() : "0",
                    formattedPrice: details.free ? "Free" : details.priceText,
                    userRatingCount: details.ratings,
                    userRatingCountForCurrentVersion: details.ratings,
                    trackContentRating: details.contentRating,
                    contentAdvisoryRating: details.contentRating,
                    trackCensoredName: details.title,
                    trackViewUrl: details.url,
                    minimumOsVersion: details.androidVersionText || "",

                    // 分类
                    genres: [details.genre],
                    price: details.price || 0.0,
                    primaryGenreName: details.genre,
                    primaryGenreId: details.genreId,
                    genreIds: [details.genreId?.toString()].filter(Boolean),

                    // 应用标识
                    bundleId: details.appId,
                    trackName: details.title,
                    trackId: details.appId,

                    // 版本信息
                    currentVersionReleaseDate: details.updated ? new Date(details.updated).toISOString() : "",
                    releaseDate: details.released ? new Date(details.released).toISOString() : "",
                    releaseNotes: details.recentChanges || "",
                    version: details.version || "",

                    // 其他
                    isVppDeviceBasedLicensingEnabled: false,
                    wrapperType: "software",
                    currency: details.currency || "USD",
                    description: details.description || "",

                    // 额外的 Google Play 特有信息
                    googlePlay: {
                        installs: details.installs,
                        minInstalls: details.minInstalls,
                        maxInstalls: details.maxInstalls,
                        containsAds: details.containsAds,
                        inAppProductPrice: details.inAppProductPrice,
                        adSupported: details.adSupported,
                        androidVersion: details.androidVersion,
                        video: details.video,
                        videoImage: details.videoImage,
                        headerImage: details.headerImage,
                        histogram: details.histogram,
                        recentReviews: reviews.data ? reviews.data.slice(0, 5).map(review => ({
                            userName: review.userName,
                            userImage: review.userImage,
                            date: review.date,
                            score: review.score,
                            scoreText: review.scoreText,
                            text: review.text
                        })) : []
                    },

                    // 获取时间
                    scrapedAt: new Date().toISOString()
                };
                
                this.detailedApps.push(detailedApp);
                
                // 每处理5个应用暂停一下，避免被限制
                if ((i + 1) % 5 === 0) {
                    console.log('⏳ 暂停2秒...');
                    await this.sleep(2000);
                }
                
            } catch (error) {
                console.error(`❌ 获取 ${app.title} 详细信息时出错:`, error.message);
                // 继续处理下一个应用
                continue;
            }
        }
        
        console.log(`✅ 成功获取 ${this.detailedApps.length} 个应用的详细信息`);
    }

    async saveBasicInfo() {
        const basicInfoPath = path.join(this.outputDir, `${this.developer.toLowerCase()}_apps_basic.json`);
        await fs.writeJson(basicInfoPath, this.apps, { spaces: 2 });
        console.log(`💾 基础信息已保存到: ${basicInfoPath}`);
    }

    async saveDetailedInfo() {
        // 保存为 JSON
        const jsonPath = path.join(this.outputDir, `${this.developer.toLowerCase()}_apps_detailed.json`);
        await fs.writeJson(jsonPath, this.detailedApps, { spaces: 2 });
        console.log(`💾 详细信息已保存到: ${jsonPath}`);

        // 保存为 CSV
        const csvPath = path.join(this.outputDir, `${this.developer.toLowerCase()}_apps_detailed.csv`);
        const csvWriter = createCsvWriter({
            path: csvPath,
            header: [
                { id: 'trackName', title: '应用名称' },
                { id: 'bundleId', title: '应用ID' },
                { id: 'artistName', title: '开发者' },
                { id: 'primaryGenreName', title: '分类' },
                { id: 'averageUserRating', title: '评分' },
                { id: 'userRatingCount', title: '评分数量' },
                { id: 'googlePlay.installs', title: '安装次数' },
                { id: 'formattedPrice', title: '价格' },
                { id: 'fileSizeBytes', title: '大小' },
                { id: 'version', title: '版本' },
                { id: 'currentVersionReleaseDate', title: '更新时间' },
                { id: 'minimumOsVersion', title: '最低系统版本' },
                { id: 'trackContentRating', title: '内容评级' },
                { id: 'googlePlay.containsAds', title: '包含广告' },
                { id: 'trackViewUrl', title: 'Store链接' },
                { id: 'scrapedAt', title: '获取时间' }
            ]
        });

        await csvWriter.writeRecords(this.detailedApps);
        console.log(`💾 CSV文件已保存到: ${csvPath}`);
    }

    async generateReport() {
        const reportPath = path.join(this.outputDir, `${this.developer.toLowerCase()}_report.md`);

        let report = `# ${this.developer} 应用分析报告

## 概览
- **开发者**: ${this.developer}
- **应用总数**: ${this.detailedApps.length}
- **报告生成时间**: ${new Date().toLocaleString('zh-CN')}

## 统计信息

### 分类分布
`;

        // 统计分类
        const genreStats = {};
        this.detailedApps.forEach(app => {
            const genre = app.primaryGenreName || 'Unknown';
            genreStats[genre] = (genreStats[genre] || 0) + 1;
        });

        Object.entries(genreStats).forEach(([genre, count]) => {
            report += `- **${genre}**: ${count} 个应用\n`;
        });

        // 评分统计
        const scores = this.detailedApps.filter(app => app.averageUserRating).map(app => app.averageUserRating);
        if (scores.length > 0) {
            const avgScore = (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(2);
            report += `\n### 评分统计
- **平均评分**: ${avgScore}
- **最高评分**: ${Math.max(...scores)}
- **最低评分**: ${Math.min(...scores)}
`;
        }

        // 热门应用（按下载量）
        report += `\n### 热门应用（按下载量排序）
`;

        const sortedApps = this.detailedApps
            .filter(app => app.googlePlay && app.googlePlay.minInstalls)
            .sort((a, b) => b.googlePlay.minInstalls - a.googlePlay.minInstalls)
            .slice(0, 10);

        sortedApps.forEach((app, index) => {
            report += `${index + 1}. **${app.trackName}** - ${app.googlePlay.installs} 次下载, 评分: ${app.averageUserRating || 'N/A'}\n`;
        });

        // 最新应用
        report += `\n### 最近更新的应用
`;

        const recentApps = this.detailedApps
            .filter(app => app.currentVersionReleaseDate)
            .sort((a, b) => new Date(b.currentVersionReleaseDate) - new Date(a.currentVersionReleaseDate))
            .slice(0, 5);

        recentApps.forEach((app, index) => {
            report += `${index + 1}. **${app.trackName}** - 更新时间: ${app.currentVersionReleaseDate}\n`;
        });

        await fs.writeFile(reportPath, report);
        console.log(`📊 分析报告已保存到: ${reportPath}`);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async run() {
        try {
            await this.init();
            await this.getDeveloperApps();
            await this.getDetailedInfo();
            await this.saveDetailedInfo();
            await this.generateReport();

            console.log('\n🎉 任务完成！');
            console.log(`📁 所有文件已保存到: ${path.resolve(this.outputDir)}`);
            console.log('\n文件列表:');
            console.log(`- ${this.developer.toLowerCase()}_apps_basic.json (基础信息)`);
            console.log(`- ${this.developer.toLowerCase()}_apps_detailed.json (详细信息JSON)`);
            console.log(`- ${this.developer.toLowerCase()}_apps_detailed.csv (详细信息CSV)`);
            console.log(`- ${this.developer.toLowerCase()}_report.md (分析报告)`);

        } catch (error) {
            console.error('❌ 程序执行出错:', error.message);
            process.exit(1);
        }
    }
}

// 运行程序 - 可以通过命令行参数指定开发者名称
const developerName = process.argv[2] || 'VOODOO';
const scraper = new AppScraper(developerName);
scraper.run();

module.exports = AppScraper;
