const gplay = require('google-play-scraper');
const fs = require('fs');
const yaml = require('js-yaml');

// 读取配置文件
function loadConfig() {
  try {
    const configFile = fs.readFileSync('gp_top_devs.yaml', 'utf8');
    return yaml.load(configFile);
  } catch (error) {
    console.error('读取配置文件失败:', error.message);
    process.exit(1);
  }
}

const config = loadConfig();
console.log('配置文件加载成功');
console.log(`输出文件: ${config.output.filename}`);
console.log(`游戏品类数量: ${config.categories.length}`);
console.log(`Collections数量: ${config.collections.length}`);
console.log(`国家数量: ${config.countries.length}`);

// 将配置中的字符串转换为gplay对象
const gameCategories = config.categories.map(cat => gplay.category[cat]);
const collections = config.collections.map(col => gplay.collection[col]);

async function scrapeGameDevelopers() {
  try {
    // 读取现有的厂商数据（如果文件存在）
    let existingDevelopers = {};
    const jsonFilePath = config.output.filename;

    if (fs.existsSync(jsonFilePath)) {
      try {
        const existingData = fs.readFileSync(jsonFilePath, 'utf8');
        const parsedData = JSON.parse(existingData);

        // 验证数据格式是否正确（应该是对象，且值应该是数组）
        if (typeof parsedData === 'object' && !Array.isArray(parsedData)) {
          // 检查是否是正确的格式：厂商名 -> 游戏数组
          let isValidFormat = true;
          for (const [, value] of Object.entries(parsedData)) {
            if (!Array.isArray(value)) {
              isValidFormat = false;
              break;
            }
          }

          if (isValidFormat) {
            existingDevelopers = parsedData;
            const existingCount = Object.keys(existingDevelopers).length;
            console.log(`读取到现有厂商数据: ${existingCount} 个厂商`);
          } else {
            console.log('现有文件格式不正确，将创建新文件');
            existingDevelopers = {};
          }
        } else {
          console.log('现有文件格式不正确（不是对象格式），将创建新文件');
          existingDevelopers = {};
        }
      } catch (error) {
        console.log('读取现有文件失败，将创建新文件');
        existingDevelopers = {};
      }
    } else {
      console.log('未找到现有文件，将创建新文件');
    }

    // 使用对象来存储厂商和对应的游戏列表
    const allDevelopers = { ...existingDevelopers };
    const initialCount = Object.keys(allDevelopers).length;

    console.log('开始抓取游戏厂商数据...');
    console.log(`将抓取 ${gameCategories.length} 个品类 × ${collections.length} 个collections × ${config.countries.length} 个国家`);

    // 遍历所有游戏品类、collections和国家
    for (let i = 0; i < gameCategories.length; i++) {
      const category = gameCategories[i];
      const categoryName = config.categories[i];
      console.log(`正在抓取品类 ${i + 1}/${gameCategories.length}: ${categoryName}`);

      // 对每个品类，尝试多个collection
      for (let j = 0; j < collections.length; j++) {
        const collection = collections[j];
        const collectionName = config.collections[j];

        // 对每个collection，尝试多个国家
        for (let k = 0; k < config.countries.length; k++) {
          const country = config.countries[k];

          try {
            if (config.debug.show_progress) {
              console.log(`  ${collectionName} - ${country.name} (${country.code})`);
            }

            const results = await gplay.list({
              category: category,
              collection: collection,
              num: config.scraping.num_per_request,
              lang: config.scraping.language,
              country: country.code,
              fullDetail: false
            });

            // 提取厂商名称和游戏名称
            results.forEach(game => {
              if (game.developer && game.developer.trim() && game.title && game.title.trim()) {
                const developer = game.developer.trim();
                const gameTitle = game.title.trim();

                // 如果厂商不存在，创建新的游戏数组
                if (!allDevelopers[developer]) {
                  allDevelopers[developer] = [];
                }

                // 避免重复添加同一个游戏
                if (!allDevelopers[developer].includes(gameTitle)) {
                  allDevelopers[developer].push(gameTitle);
                }
              }
            });

            // 添加延迟避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, config.scraping.delay_ms));

          } catch (error) {
            console.error(`抓取 ${categoryName} - ${collectionName} - ${country.name} 时出错:`, error.message);
            continue; // 继续处理下一个国家
          }
        }
      }

      const currentCount = Object.keys(allDevelopers).length;
      console.log(`品类 ${categoryName} 完成，当前总厂商数: ${currentCount}`);
    }

    // 验证数据结构正确性
    if (config.debug.show_preview) {
      console.log('\n验证数据结构...');
      const sampleDevelopers = Object.keys(allDevelopers).slice(0, 3);
      sampleDevelopers.forEach(dev => {
        console.log(`${dev}: [${allDevelopers[dev].slice(0, 2).join(', ')}${allDevelopers[dev].length > 2 ? '...' : ''}]`);
      });
    }

    // 保存所有厂商和游戏数据到 JSON 文件
    fs.writeFileSync(jsonFilePath, JSON.stringify(allDevelopers, null, 2));

    const finalCount = Object.keys(allDevelopers).length;
    const totalGames = Object.values(allDevelopers).reduce((sum, games) => sum + games.length, 0);

    console.log(`\n抓取完成！`);
    console.log(`原有厂商数: ${initialCount}`);
    console.log(`新增厂商数: ${finalCount - initialCount}`);
    console.log(`总厂商数: ${finalCount}`);
    console.log(`总游戏数: ${totalGames}`);
    console.log(`已保存所有数据到 ${jsonFilePath}`);

    // 打印厂商及其游戏作为预览
    if (config.debug.show_preview) {
      console.log(`\n前${config.debug.preview_count}个厂商及其游戏预览:`);
      const developerNames = Object.keys(allDevelopers);
      developerNames.slice(0, config.debug.preview_count).forEach((developer, index) => {
        const games = allDevelopers[developer];
        const gameCount = games ? games.length : 0;
        if (gameCount > 0) {
          const gamePreview = games.slice(0, 3).join(', ');
          console.log(`${index + 1}. ${developer} (${gameCount}个游戏): ${gamePreview}${gameCount > 3 ? '...' : ''}`);
        } else {
          console.log(`${index + 1}. ${developer} (0个游戏)`);
        }
      });

      // 如果有新增厂商，显示最新添加的一些厂商
      if (finalCount > initialCount) {
        console.log('\n最新添加的厂商预览:');
        const newDevelopers = developerNames.slice(initialCount, initialCount + 5);
        newDevelopers.forEach((developer, index) => {
          const games = allDevelopers[developer];
          const gameCount = games ? games.length : 0;
          if (gameCount > 0) {
            const gamePreview = games.slice(0, 3).join(', ');
            console.log(`${index + 1}. ${developer} (${gameCount}个游戏): ${gamePreview}${gameCount > 3 ? '...' : ''}`);
          } else {
            console.log(`${index + 1}. ${developer} (0个游戏)`);
          }
        });
      }
    }

  } catch (error) {
    console.error('抓取过程中发生错误:', error);
  }
}

scrapeGameDevelopers();